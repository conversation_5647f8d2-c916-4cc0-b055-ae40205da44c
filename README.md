# 充值次数卡密系统

一个基于Spring Boot + MyBatis + Vue的充值次数卡密系统，支持卡密生成、充值、次数管理等功能。

## 技术栈

- **后端**: Java 21 + Spring Boot 3.2 + MyBatis Plus
- **数据库**: MySQL 8.0
- **前端**: Vue 3 + Element Plus（待开发）
- **连接池**: Druid

## 功能特性

### 核心功能
- ✅ 卡密生成（支持批量生成）
- ✅ 卡密充值（增加用户可用次数）
- ✅ 次数使用（扣减用户可用次数）
- ✅ 用户次数查询
- ✅ 充值记录查询
- ✅ 使用记录查询

### 管理功能
- ✅ 卡密状态管理（未使用/已使用/已过期）
- ✅ 批次管理
- ✅ 过期卡密自动更新
- ✅ 统计信息查询

## 快速开始

### 1. 环境要求
- Java 21+
- MySQL 8.0+
- Maven 3.6+

### 2. 数据库配置
1. 创建数据库：
```sql
CREATE DATABASE recharge_card_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行SQL脚本：
```bash
mysql -u root -p recharge_card_db < src/main/resources/db/schema.sql
```

3. 修改数据库连接配置（`src/main/resources/application.yml`）：
```yaml
spring:
  datasource:
    url: *****************************************************************************************************************************
    username: root
    password: 你的密码
```

### 3. 启动应用
```bash
# 编译
mvn clean compile

# 启动
mvn spring-boot:run
```

### 4. 访问系统
- 应用地址: http://localhost:8080
- 测试页面: http://localhost:8080/index.html
- Druid监控: http://localhost:8080/druid (admin/admin)

## API接口

### 卡密管理
- `POST /api/cards/generate` - 生成卡密
- `POST /api/cards/recharge` - 充值
- `GET /api/cards/validate/{cardCode}` - 验证卡密
- `GET /api/cards/{cardCode}` - 查询卡密信息
- `GET /api/cards/batch/{batchId}` - 查询批次卡密
- `GET /api/cards/statistics` - 获取统计信息

### 用户次数管理
- `GET /api/user-times/{userId}` - 获取用户次数信息
- `POST /api/user-times/use` - 使用次数
- `GET /api/user-times/{userId}/check/{times}` - 检查是否有足够次数
- `GET /api/user-times/{userId}/available` - 获取可用次数

### 记录查询
- `GET /api/records/recharge/{userId}` - 获取充值记录
- `GET /api/records/usage/{userId}` - 获取使用记录
- `GET /api/records/recharge/recent` - 获取最近充值记录
- `GET /api/records/usage/recent` - 获取最近使用记录

## 使用示例

### 1. 生成卡密
```bash
curl -X POST http://localhost:8080/api/cards/generate \
  -H "Content-Type: application/json" \
  -d '{
    "cardType": "10次卡",
    "rechargeTimes": 10,
    "count": 5,
    "validDays": 365
  }'
```

### 2. 充值
```bash
curl -X POST http://localhost:8080/api/cards/recharge \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user001",
    "cardCode": "RC1234567890ABCD"
  }'
```

### 3. 使用次数
```bash
curl -X POST http://localhost:8080/api/user-times/use \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user001",
    "usedTimes": 1,
    "usageType": "下载文件"
  }'
```

### 4. 查询用户次数
```bash
curl http://localhost:8080/api/user-times/user001
```

## 数据库表结构

### recharge_cards（充值卡表）
- `id` - 主键
- `card_code` - 卡密（16位）
- `card_type` - 卡类型
- `recharge_times` - 可充值次数
- `status` - 状态（unused/used/expired）
- `expire_at` - 过期时间
- `batch_id` - 批次号

### user_times（用户次数表）
- `id` - 主键
- `user_id` - 用户ID
- `available_times` - 可用次数
- `total_recharged` - 总充值次数
- `total_used` - 总使用次数

### recharge_records（充值记录表）
- `id` - 主键
- `user_id` - 用户ID
- `card_code` - 卡密
- `recharge_times` - 充值次数

### usage_records（使用记录表）
- `id` - 主键
- `user_id` - 用户ID
- `used_times` - 使用次数
- `remaining_times` - 剩余次数
- `usage_type` - 使用类型

## 开发计划

- [ ] Vue 3前端界面开发
- [ ] 用户认证和权限管理
- [ ] 卡密导出功能
- [ ] 数据统计图表
- [ ] 定时任务（自动更新过期卡密）
- [ ] 接口限流和防刷
- [ ] 日志审计功能

## 许可证

MIT License
