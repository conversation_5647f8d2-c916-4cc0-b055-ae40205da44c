-- 创建数据库
CREATE DATABASE IF NOT EXISTS recharge_card_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE recharge_card_db;

-- 充值卡表
CREATE TABLE recharge_cards (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    card_code VARCHAR(32) UNIQUE NOT NULL COMMENT '卡密',
    card_type VARCHAR(20) NOT NULL COMMENT '卡类型(次数卡10次/50次等)',
    recharge_times INT NOT NULL COMMENT '可充值次数',
    status ENUM('unused', 'used', 'expired') DEFAULT 'unused' COMMENT '状态：unused-未使用，used-已使用，expired-已过期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    used_at TIMESTAMP NULL COMMENT '使用时间',
    used_by VARCHAR(50) NULL COMMENT '使用者ID',
    expire_at TIMESTAMP NULL COMMENT '过期时间',
    batch_id VARCHAR(32) NULL COMMENT '批次号',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标记',
    INDEX idx_card_code (card_code),
    INDEX idx_status (status),
    INDEX idx_batch_id (batch_id),
    INDEX idx_card_type (card_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值卡表';

-- 用户次数表
CREATE TABLE user_times (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id VARCHAR(50) UNIQUE NOT NULL COMMENT '用户ID',
    available_times INT DEFAULT 0 COMMENT '可用次数',
    total_recharged INT DEFAULT 0 COMMENT '总充值次数',
    total_used INT DEFAULT 0 COMMENT '总使用次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标记',
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户次数表';

-- 充值记录表
CREATE TABLE recharge_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    card_code VARCHAR(32) NOT NULL COMMENT '卡密',
    recharge_times INT NOT NULL COMMENT '充值次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标记',
    INDEX idx_user_id (user_id),
    INDEX idx_card_code (card_code),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值记录表';

-- 使用记录表
CREATE TABLE usage_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    used_times INT DEFAULT 1 COMMENT '使用次数',
    remaining_times INT NOT NULL COMMENT '剩余次数',
    usage_type VARCHAR(50) COMMENT '使用类型/场景',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标记',
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='使用记录表';

-- 插入一些测试数据
INSERT INTO recharge_cards (card_code, card_type, recharge_times, expire_at, batch_id) VALUES
('RC1001234567890A', '10次卡', 10, DATE_ADD(NOW(), INTERVAL 1 YEAR), 'BATCH001'),
('RC1001234567890B', '20次卡', 20, DATE_ADD(NOW(), INTERVAL 1 YEAR), 'BATCH001'),
('RC1001234567890C', '50次卡', 50, DATE_ADD(NOW(), INTERVAL 1 YEAR), 'BATCH001'),
('RC1001234567890D', '100次卡', 100, DATE_ADD(NOW(), INTERVAL 1 YEAR), 'BATCH002');
