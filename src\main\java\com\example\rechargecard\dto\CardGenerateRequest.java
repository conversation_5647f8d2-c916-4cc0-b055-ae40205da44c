package com.example.rechargecard.dto;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 卡密生成请求DTO
 */
@Data
public class CardGenerateRequest {

    /**
     * 卡类型
     */
    @NotBlank(message = "卡类型不能为空")
    private String cardType;

    /**
     * 充值次数
     */
    @NotNull(message = "充值次数不能为空")
    @Min(value = 10, message = "充值次数至少为10次")
    private Integer rechargeTimes;

    /**
     * 生成数量
     */
    @NotNull(message = "生成数量不能为空")
    @Min(value = 1, message = "生成数量至少为1")
    private Integer count;

    /**
     * 有效期（天数）
     */
    @Min(value = 1, message = "有效期至少为1天")
    private Integer validDays = 365; // 默认1年

    /**
     * 批次号（可选，不填则自动生成）
     */
    private String batchId;
}
