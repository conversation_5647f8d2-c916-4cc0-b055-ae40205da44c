package com.example.rechargecard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.rechargecard.entity.RechargeCard;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 充值卡Mapper接口
 */
@Mapper
public interface RechargeCardMapper extends BaseMapper<RechargeCard> {

    /**
     * 根据卡密查询充值卡
     */
    @Select("SELECT * FROM recharge_cards WHERE card_code = #{cardCode} AND deleted = 0")
    RechargeCard selectByCardCode(@Param("cardCode") String cardCode);

    /**
     * 更新卡密状态为已使用
     */
    @Update("UPDATE recharge_cards SET status = 'used', used_at = #{usedAt}, used_by = #{usedBy} " +
            "WHERE card_code = #{cardCode} AND status = 'unused' AND deleted = 0")
    int updateCardAsUsed(@Param("cardCode") String cardCode, 
                        @Param("usedAt") LocalDateTime usedAt, 
                        @Param("usedBy") String usedBy);

    /**
     * 根据批次号查询卡密列表
     */
    @Select("SELECT * FROM recharge_cards WHERE batch_id = #{batchId} AND deleted = 0 ORDER BY created_at DESC")
    List<RechargeCard> selectByBatchId(@Param("batchId") String batchId);

    /**
     * 统计各状态的卡密数量
     */
    @Select("SELECT status, COUNT(*) as count FROM recharge_cards WHERE deleted = 0 GROUP BY status")
    List<Object> countByStatus();

    /**
     * 查询即将过期的卡密（7天内过期）
     */
    @Select("SELECT * FROM recharge_cards WHERE status = 'unused' AND expire_at <= DATE_ADD(NOW(), INTERVAL 7 DAY) AND deleted = 0")
    List<RechargeCard> selectExpiringSoon();

    /**
     * 批量更新过期卡密状态
     */
    @Update("UPDATE recharge_cards SET status = 'expired' WHERE status = 'unused' AND expire_at < NOW() AND deleted = 0")
    int updateExpiredCards();
}
