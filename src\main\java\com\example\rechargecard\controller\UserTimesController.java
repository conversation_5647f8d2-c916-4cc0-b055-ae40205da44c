package com.example.rechargecard.controller;

import com.example.rechargecard.common.Result;
import com.example.rechargecard.dto.UsageRequest;
import com.example.rechargecard.entity.UserTimes;
import com.example.rechargecard.service.UserTimesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户次数控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/user-times")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class UserTimesController {

    private final UserTimesService userTimesService;

    /**
     * 获取用户次数信息
     */
    @GetMapping("/{userId}")
    public Result<UserTimes> getUserTimes(@PathVariable String userId) {
        try {
            UserTimes userTimes = userTimesService.getUserTimes(userId);
            return Result.success(userTimes);
        } catch (Exception e) {
            log.error("获取用户次数信息失败", e);
            return Result.error("获取用户次数信息失败：" + e.getMessage());
        }
    }

    /**
     * 使用次数
     */
    @PostMapping("/use")
    public Result<Void> useTimes(@Validated @RequestBody UsageRequest request) {
        try {
            userTimesService.useTimes(request);
            return Result.success("使用次数成功");
        } catch (Exception e) {
            log.error("使用次数失败", e);
            return Result.error("使用次数失败：" + e.getMessage());
        }
    }

    /**
     * 检查用户是否有足够次数
     */
    @GetMapping("/{userId}/check/{times}")
    public Result<Boolean> checkTimes(@PathVariable String userId, @PathVariable Integer times) {
        try {
            boolean hasEnough = userTimesService.hasEnoughTimes(userId, times);
            return Result.success(hasEnough);
        } catch (Exception e) {
            log.error("检查用户次数失败", e);
            return Result.error("检查用户次数失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户当前可用次数
     */
    @GetMapping("/{userId}/available")
    public Result<Integer> getAvailableTimes(@PathVariable String userId) {
        try {
            Integer times = userTimesService.getAvailableTimes(userId);
            return Result.success(times);
        } catch (Exception e) {
            log.error("获取用户可用次数失败", e);
            return Result.error("获取用户可用次数失败：" + e.getMessage());
        }
    }
}
