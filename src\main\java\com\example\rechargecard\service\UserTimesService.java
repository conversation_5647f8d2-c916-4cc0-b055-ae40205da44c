package com.example.rechargecard.service;

import com.example.rechargecard.dto.UsageRequest;
import com.example.rechargecard.entity.UserTimes;

/**
 * 用户次数服务接口
 */
public interface UserTimesService {

    /**
     * 获取用户次数信息
     */
    UserTimes getUserTimes(String userId);

    /**
     * 创建用户次数记录
     */
    UserTimes createUserTimes(String userId);

    /**
     * 增加用户可用次数
     */
    boolean addTimes(String userId, Integer times);

    /**
     * 使用次数
     */
    boolean useTimes(UsageRequest request);

    /**
     * 检查用户是否有足够次数
     */
    boolean hasEnoughTimes(String userId, Integer times);

    /**
     * 获取用户当前可用次数
     */
    Integer getAvailableTimes(String userId);
}
