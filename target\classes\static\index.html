<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充值次数卡密系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .row {
            display: flex;
            gap: 20px;
        }
        .col {
            flex: 1;
        }
    </style>
</head>
<body>
    <h1>充值次数卡密系统</h1>
    
    <div class="row">
        <!-- 生成卡密 -->
        <div class="col">
            <div class="container">
                <h2>生成卡密</h2>
                <div class="form-group">
                    <label>卡类型:</label>
                    <input type="text" id="cardType" value="10次卡" placeholder="例如：10次卡">
                </div>
                <div class="form-group">
                    <label>充值次数:</label>
                    <input type="number" id="rechargeTimes" value="10" min="10">
                </div>
                <div class="form-group">
                    <label>生成数量:</label>
                    <input type="number" id="count" value="1" min="1">
                </div>
                <div class="form-group">
                    <label>有效期(天):</label>
                    <input type="number" id="validDays" value="365" min="1">
                </div>
                <button onclick="generateCards()">生成卡密</button>
                <div id="generateResult" class="result" style="display:none;"></div>
            </div>
        </div>

        <!-- 充值 -->
        <div class="col">
            <div class="container">
                <h2>充值</h2>
                <div class="form-group">
                    <label>用户ID:</label>
                    <input type="text" id="userId" placeholder="输入用户ID">
                </div>
                <div class="form-group">
                    <label>卡密:</label>
                    <input type="text" id="cardCode" placeholder="输入卡密">
                </div>
                <button onclick="recharge()">充值</button>
                <div id="rechargeResult" class="result" style="display:none;"></div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 查询用户次数 -->
        <div class="col">
            <div class="container">
                <h2>查询用户次数</h2>
                <div class="form-group">
                    <label>用户ID:</label>
                    <input type="text" id="queryUserId" placeholder="输入用户ID">
                </div>
                <button onclick="queryUserTimes()">查询次数</button>
                <div id="queryResult" class="result" style="display:none;"></div>
            </div>
        </div>

        <!-- 使用次数 -->
        <div class="col">
            <div class="container">
                <h2>使用次数</h2>
                <div class="form-group">
                    <label>用户ID:</label>
                    <input type="text" id="useUserId" placeholder="输入用户ID">
                </div>
                <div class="form-group">
                    <label>使用次数:</label>
                    <input type="number" id="usedTimes" value="1" min="1">
                </div>
                <div class="form-group">
                    <label>使用类型:</label>
                    <input type="text" id="usageType" placeholder="例如：下载文件">
                </div>
                <button onclick="useTimes()">使用次数</button>
                <button onclick="testDownload()" style="background-color: #28a745; margin-top: 5px;">测试下载文件</button>
                <div id="useResult" class="result" style="display:none;"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';

        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.style.display = 'block';
        }

        async function generateCards() {
            const data = {
                cardType: document.getElementById('cardType').value,
                rechargeTimes: parseInt(document.getElementById('rechargeTimes').value),
                count: parseInt(document.getElementById('count').value),
                validDays: parseInt(document.getElementById('validDays').value)
            };

            try {
                const response = await fetch(`${API_BASE}/cards/generate`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                
                if (result.code === 200) {
                    const cards = result.data.map(card => card.cardCode).join('\n');
                    showResult('generateResult', `生成成功！\n卡密列表：\n${cards}`, true);
                } else {
                    showResult('generateResult', `生成失败：${result.message}`, false);
                }
            } catch (error) {
                showResult('generateResult', `请求失败：${error.message}`, false);
            }
        }

        async function recharge() {
            const data = {
                userId: document.getElementById('userId').value,
                cardCode: document.getElementById('cardCode').value
            };

            try {
                const response = await fetch(`${API_BASE}/cards/recharge`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                
                if (result.code === 200) {
                    showResult('rechargeResult', '充值成功！', true);
                } else {
                    showResult('rechargeResult', `充值失败：${result.message}`, false);
                }
            } catch (error) {
                showResult('rechargeResult', `请求失败：${error.message}`, false);
            }
        }

        async function queryUserTimes() {
            const userId = document.getElementById('queryUserId').value;

            try {
                const response = await fetch(`${API_BASE}/user-times/${userId}`);
                const result = await response.json();
                
                if (result.code === 200) {
                    const data = result.data;
                    const message = `用户次数信息：
可用次数：${data.availableTimes}
总充值次数：${data.totalRecharged}
总使用次数：${data.totalUsed}
更新时间：${data.updatedAt}`;
                    showResult('queryResult', message, true);
                } else {
                    showResult('queryResult', `查询失败：${result.message}`, false);
                }
            } catch (error) {
                showResult('queryResult', `请求失败：${error.message}`, false);
            }
        }

        async function useTimes() {
            const userId = document.getElementById('useUserId').value;
            const usedTimes = parseInt(document.getElementById('usedTimes').value);
            const usageType = document.getElementById('usageType').value;

            // 验证输入
            if (!userId) {
                showResult('useResult', '请输入用户ID', false);
                return;
            }

            // 直接下载文件，不依赖后端接口
            showResult('useResult', '正在下载文件...', true);
            downloadFile(userId, usageType);

            // 可选：如果后端正常，也可以记录使用次数
            try {
                const data = {
                    userId: userId,
                    usedTimes: usedTimes,
                    usageType: usageType
                };

                const response = await fetch(`${API_BASE}/user-times/use`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                // 不管后端是否成功，都继续下载
                console.log('后端记录结果:', await response.json());
            } catch (error) {
                console.log('后端记录失败:', error.message);
                // 即使后端失败，也继续下载
            }
        }

        function downloadFile(userId, usageType) {
            console.log('开始下载文件，用户ID:', userId, '使用类型:', usageType);

            try {
                // 创建文件内容
                const currentTime = new Date().toLocaleString('zh-CN');
                const fileContent = `==========================================
        用户文件下载记录
==========================================

用户ID: ${userId}
下载时间: ${currentTime}
使用类型: ${usageType || '文件下载'}
文件版本: v1.0

==========================================
        文件内容说明
==========================================

这是一个示例文件，用于演示充值次数卡密系统的文件下载功能。

系统功能包括：
1. 卡密生成与管理
2. 用户充值功能
3. 次数使用与记录
4. 文件下载服务

感谢使用充值次数卡密系统！

==========================================
        技术支持
==========================================

如有问题，请联系系统管理员。
系统版本: 1.0.0
更新日期: ${currentTime}

==========================================`;

                // 创建Blob对象
                const blob = new Blob([fileContent], { type: 'text/plain;charset=utf-8' });

                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `user_${userId}_${new Date().getTime()}.txt`;

                // 添加到DOM并触发下载
                document.body.appendChild(a);
                a.click();

                // 清理
                setTimeout(() => {
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                }, 100);

                // 更新结果显示
                setTimeout(() => {
                    showResult('useResult', '文件下载成功！请检查浏览器下载文件夹。', true);
                }, 500);

                console.log('文件下载完成');

            } catch (error) {
                console.error('下载文件失败:', error);
                showResult('useResult', `下载失败：${error.message}`, false);
            }
        }

        function testDownload() {
            const userId = document.getElementById('useUserId').value || 'test_user';
            const usageType = document.getElementById('usageType').value || '测试下载';

            console.log('测试下载，用户ID:', userId);
            showResult('useResult', '正在测试下载...', true);
            downloadFile(userId, usageType);
        }

        // 备用方法：前端生成文件下载
        function downloadFileLocal(userId, usageType) {
            // 创建一个示例文件内容
            const fileContent = `用户文件下载记录
用户ID: ${userId}
下载时间: ${new Date().toLocaleString()}
使用类型: ${usageType || '文件下载'}
文件说明: 这是一个示例文件，用于演示下载功能。

感谢使用充值次数卡密系统！`;

            // 创建Blob对象
            const blob = new Blob([fileContent], { type: 'text/plain;charset=utf-8' });

            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `user_${userId}_${new Date().getTime()}.txt`;

            // 触发下载
            document.body.appendChild(a);
            a.click();

            // 清理
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            // 更新结果显示
            setTimeout(() => {
                showResult('useResult', '使用次数成功！文件已下载。', true);
            }, 1000);
        }
    </script>
</body>
</html>
