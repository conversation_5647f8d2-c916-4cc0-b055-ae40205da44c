package com.example.rechargecard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.rechargecard.entity.RechargeRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 充值记录Mapper接口
 */
@Mapper
public interface RechargeRecordMapper extends BaseMapper<RechargeRecord> {

    /**
     * 根据用户ID查询充值记录
     */
    @Select("SELECT * FROM recharge_records WHERE user_id = #{userId} AND deleted = 0 ORDER BY created_at DESC")
    List<RechargeRecord> selectByUserId(@Param("userId") String userId);

    /**
     * 根据卡密查询充值记录
     */
    @Select("SELECT * FROM recharge_records WHERE card_code = #{cardCode} AND deleted = 0")
    RechargeRecord selectByCardCode(@Param("cardCode") String cardCode);

    /**
     * 统计用户总充值次数
     */
    @Select("SELECT COALESCE(SUM(recharge_times), 0) FROM recharge_records WHERE user_id = #{userId} AND deleted = 0")
    Integer sumRechargeTimesByUserId(@Param("userId") String userId);

    /**
     * 查询最近的充值记录
     */
    @Select("SELECT * FROM recharge_records WHERE deleted = 0 ORDER BY created_at DESC LIMIT #{limit}")
    List<RechargeRecord> selectRecentRecords(@Param("limit") Integer limit);
}
