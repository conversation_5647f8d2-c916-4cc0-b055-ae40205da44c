package com.example.rechargecard.controller;

import com.example.rechargecard.common.Result;
import com.example.rechargecard.dto.CardGenerateRequest;
import com.example.rechargecard.dto.RechargeRequest;
import com.example.rechargecard.entity.RechargeCard;
import com.example.rechargecard.service.RechargeCardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 充值卡控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/cards")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class RechargeCardController {

    private final RechargeCardService rechargeCardService;

    /**
     * 生成卡密
     */
    @PostMapping("/generate")
    public Result<List<RechargeCard>> generateCards(@Validated @RequestBody CardGenerateRequest request) {
        try {
            List<RechargeCard> cards = rechargeCardService.generateCards(request);
            return Result.success("卡密生成成功", cards);
        } catch (Exception e) {
            log.error("生成卡密失败", e);
            return Result.error("生成卡密失败：" + e.getMessage());
        }
    }

    /**
     * 充值
     */
    @PostMapping("/recharge")
    public Result<Void> recharge(@Validated @RequestBody RechargeRequest request) {
        try {
            rechargeCardService.recharge(request);
            return Result.success("充值成功");
        } catch (Exception e) {
            log.error("充值失败", e);
            return Result.error("充值失败：" + e.getMessage());
        }
    }

    /**
     * 验证卡密
     */
    @GetMapping("/validate/{cardCode}")
    public Result<Boolean> validateCard(@PathVariable String cardCode) {
        try {
            boolean isValid = rechargeCardService.validateCard(cardCode);
            return Result.success(isValid);
        } catch (Exception e) {
            log.error("验证卡密失败", e);
            return Result.error("验证卡密失败：" + e.getMessage());
        }
    }

    /**
     * 查询卡密信息
     */
    @GetMapping("/{cardCode}")
    public Result<RechargeCard> getCard(@PathVariable String cardCode) {
        try {
            RechargeCard card = rechargeCardService.getCardByCode(cardCode);
            if (card == null) {
                return Result.notFound("卡密不存在");
            }
            return Result.success(card);
        } catch (Exception e) {
            log.error("查询卡密失败", e);
            return Result.error("查询卡密失败：" + e.getMessage());
        }
    }

    /**
     * 根据批次号查询卡密列表
     */
    @GetMapping("/batch/{batchId}")
    public Result<List<RechargeCard>> getCardsByBatch(@PathVariable String batchId) {
        try {
            List<RechargeCard> cards = rechargeCardService.getCardsByBatchId(batchId);
            return Result.success(cards);
        } catch (Exception e) {
            log.error("查询批次卡密失败", e);
            return Result.error("查询批次卡密失败：" + e.getMessage());
        }
    }

    /**
     * 获取卡密统计信息
     */
    @GetMapping("/statistics")
    public Result<Object> getStatistics() {
        try {
            Object statistics = rechargeCardService.getCardStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return Result.error("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 查询即将过期的卡密
     */
    @GetMapping("/expiring-soon")
    public Result<List<RechargeCard>> getExpiringSoonCards() {
        try {
            List<RechargeCard> cards = rechargeCardService.getExpiringSoonCards();
            return Result.success(cards);
        } catch (Exception e) {
            log.error("查询即将过期卡密失败", e);
            return Result.error("查询即将过期卡密失败：" + e.getMessage());
        }
    }

    /**
     * 更新过期卡密状态
     */
    @PostMapping("/update-expired")
    public Result<Integer> updateExpiredCards() {
        try {
            int count = rechargeCardService.updateExpiredCards();
            return Result.success("更新完成", count);
        } catch (Exception e) {
            log.error("更新过期卡密失败", e);
            return Result.error("更新过期卡密失败：" + e.getMessage());
        }
    }
}
